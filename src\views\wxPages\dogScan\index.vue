<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-08 15:22:05
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-08 15:24:45
-->
<!--犬类扫一扫-->
<template>
  <div class="dog-scan">
    <van-cell-group inset>
      <van-cell title="扫一扫" is-link clickable @click="onScan">
        <template #icon>
          <img class="scan-icon" :src="scanIcon" alt="scan" />
        </template>
      </van-cell>
    </van-cell-group>
  </div>
</template>

<script>
import { myRevelation } from "@/api/common";
import { login, getInfo } from "@/api/login";
import { setToken, getToken } from "@/util/auth";

export default {
  name: "dogScan",
  mixins: [],
  components: {},
  data() {
    return {
      scanIcon: require("@/assets/wxPages/dog_scan.png"),
    };
  },
  created() {},
  mounted() {
    //网页授权回调 携带code
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    if (params.get("code")) {
      //这里走你的登录逻辑或者干其他的啥随你遍咯
      let code = params.get("code");
      console.log(code, "2222code");
      const token = getToken();
      if (!token) {
        this.loginWx(code);
      }
    }
  },
  methods: {
    loginWx(code) {
      login({ wxCode: code })
        .then((res) => {
          setToken(res.token);
          this.GetInfo(res.token);
          this.getDictsList();
        })
        .catch((error) => {
          this.$toast.fail(error.msg);
        });
    },
    GetInfo(token) {
      getInfo(token).then((res) => {
        const user = res.user;
        const userObj = {
          deptName: user?.dept?.deptName || "",
          deptId: user?.dept?.deptId || "",
          nickName: user.nickName,
          ...user,
          id: res?.dogUserQualifi?.id || "",
          realName: user.userName || "",
          mobile: user.phonenumber || "",
          userQualifi: res?.dogUserQualifi || undefined,
          qualifi: res?.dogQualifi || undefined,
          roleType: res.roleType,
          roles: res.roles,
          userType: user.userType,
          userId: user.userId,
        };
        this.vuex("user", userObj);
        this.initParams();
      });
    },

    initParams() {
      this.formData.source = "3"; //移动端默认微信上报
      this.formData.createid = this.user.nickName;
    },
    //获取URL中的参数
    getUrlParams(url) {
      // 创建空对象存储参数
      let obj = "";
      if (url.split("?")[1]) {
        // 通过 ? 分割获取后面的参数字符串
        let urlStr = url.split("?")[0];
        let urlStr1 = url.split("?")[1];
        if (urlStr == "https://www.jinhuadog.com/pet") {
          obj = "https://ygf.xzzfj.jinhua.gov.cn/ygf/pet?" + urlStr1;
        }
      }
      return obj;
    },

    onScan() {
      const wx = window.wx;
      if (!wx || !wx.scanQRCode) {
        this.$toast("请在微信内打开并完成JSSDK配置后使用");
        return;
      }
      try {
        wx.scanQRCode({
          needResult: 1,
          scanType: ["qrCode", "barCode"],
          success: (res) => {
            const result = res.resultStr || res.scanCodeMessage || "";
            this.$toast.success("扫描成功");
            console.log("scan result:", result);
            // if (this.getUrlParams(res.text)) {
            //   let url = this.getUrlParams(res.text);
            //   location.href = url;
            // }
          },
          fail: () => {
            this.$toast.fail("扫描失败，请重试");
          },
        });
      } catch (e) {
        this.$toast.fail("当前环境不支持扫一扫");
      }
    },
  },
  watch: {},
};
</script>

<style lang="scss" scoped>
.dog-scan {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 15px;
}

/* 垂直居中 van-cell 内的图标、标题与右侧箭头 */
:deep(.van-cell) {
  align-items: center;
}
:deep(.van-cell__left-icon),
:deep(.van-cell__right-icon),
:deep(.van-cell__title) {
  display: flex;
  align-items: center;
}

.scan-icon {
  width: 40px;
  height: 40px;
  margin-right: 8px;
  border-radius: 6px;
}
</style>
